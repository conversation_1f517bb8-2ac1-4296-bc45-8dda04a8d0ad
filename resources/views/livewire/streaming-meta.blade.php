<div wire:poll.2000ms class="widget">
{{-- <div class="widget"> --}}

    <div class="row meta">
        <table class="table table-striped podio">
        <thead class="bg-naranja">
            <tr>
                <th colspan="4" scope="col">META {{$nombre}}</th>
            </tr>
        </thead>
        <tbody>
@foreach ($terminados as $key => $terminado)
            <tr>
    @if (isset($terminado->nacionalidad))
                <td class="nacionalidad" style="width: 5px; -webkit-text-stroke: 1px white; color: #D41E4A;"><img src="{{ asset('') }}img/banderas/{{$terminado->nacionalidad}}.png"></td>
    @else
                <td class="idparticipante">{{$terminado->idparticipante}}</td>
    @endif
                <td class="nombre">{{$terminado->nombre}}<br><small>{{$terminado->categorias->nombre}}</small></td>
                <td class="text-right tiempo">{{$terminado->tiempo_listo}}</td>
    @if ($terminado->vuelta > 0)
                <td class="vueltas">{{$terminado->vuelta}}</td>
    @endif
            </tr>
@endforeach
@if (isset($prellegados) && count($prellegados) > 0)
    <tr><td class="chroma" colspan="4"></td></tr>
    @foreach ($prellegados as $key => $prellegado)
            <tr>
                <td class="idparticipante">{{$prellegado->idparticipante}}</td>
                <td class="nombre">{{$prellegado->nombre}}<br><small>{{$prellegado->categorias->nombre}}</small></td>
                <td class="text-right tiempo">Coming</td>
                <td class="vueltas">{{$prellegado->vuelta}}</td>
            </tr>
    @endforeach
@endif
        </tbody>
        </table>
    </div>

</div>
