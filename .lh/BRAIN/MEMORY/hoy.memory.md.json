{"sourceFile": "BRAIN/MEMORY/hoy.memory.md", "activeCommit": 0, "commits": [{"activePatchIndex": 8, "patches": [{"date": 1726933388491, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1729834147649, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,4 +1,5 @@\n - Cada tarea intentar primero con AI\n - Cada proceso con AI va a framework\n - alias copy='xsel --clipboard --input'\n - alias paste='xsel --clipboard --output'\n+- alias ai\n"}, {"date": 1729834698983, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,2 @@\n - Cada tarea intentar primero con AI\n-- Cada proceso con AI va a framework\n-- alias copy='xsel --clipboard --input'\n-- alias paste='xsel --clipboard --output'\n-- alias ai\n+- Probar el nuevo alias ai\n"}, {"date": 1729835310290, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,2 +1,4 @@\n - Cada tarea intentar primero con AI\n-- Probar el nuevo alias ai\n+- Probar el nuevo alias ai en Debian\n+- Probar el nuevo brain.andresmisiak.ar en celular\n+- Probar el nuevo ai.andresmisiak.ar en celular\n\\ No newline at end of file\n"}, {"date": 1730281540508, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,4 +1,1 @@\n - Cada tarea intentar primero con AI\n-- Probar el nuevo alias ai en Debian\n-- Probar el nuevo brain.andresmisiak.ar en celular\n-- Probar el nuevo ai.andresmisiak.ar en celular\n\\ No newline at end of file\n"}, {"date": 1738848058929, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,1 +1,4 @@\n-- Cada tarea intentar primero con AI\n+- No hay que hacerlo todo\n+- No todas las oportunidades son válidas\n+- No pasa nada si no hacemos TikTok o si no nos ponemos a hacer SEO\n+- A lo mejor, lo que estás haciendo ya es suficiente para obtener los resultados que quieres.\n\\ No newline at end of file\n"}, {"date": 1745932734170, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,3 @@\n+- No hay que hacerlo todo\n+- Dream in years, plan in months, evaluate in weeks, ship daily\n+- A lo mejor, lo que estás haciendo ya es suficiente para obtener los resultados que quieres\n"}, {"date": 1746651237337, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,3 +1,4 @@\n - No hay que hacerlo todo\n - Dream in years, plan in months, evaluate in weeks, ship daily\n - A lo mejor, lo que estás haciendo ya es suficiente para obtener los resultados que quieres\n+- Ya tengo script para transcribir\n"}, {"date": 1751631050186, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,5 @@\n - No hay que hacerlo todo\n - Dream in years, plan in months, evaluate in weeks, ship daily\n - A lo mejor, lo que estás haciendo ya es suficiente para obtener los resultados que quieres\n - Ya tengo script para transcribir\n-- No hay que hacerlo todo\n-- No todas las oportunidades son válidas\n-- No pasa nada si no hacemos TikTok o si no nos ponemos a hacer SEO\n-- A lo mejor, lo que estás haciendo ya es suficiente para obtener los resultados que quieres.\n\\ No newline at end of file\n+- Ver de anotar tareas de otros\n"}], "date": 1726933388491, "name": "Commit-0", "content": "- Cada tarea intentar primero con AI\n- Cada proceso con AI va a framework\n- alias copy='xsel --clipboard --input'\n- alias paste='xsel --clipboard --output'\n"}]}