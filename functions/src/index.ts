// import * as functions from "firebase-functions";

const functions = require('firebase-functions');
const admin = require("firebase-admin");
admin.initializeApp();

exports.addRead = functions.https.onRequest( async (req, res) => {
    const checkpoint = req.query.checkpoint;
    const bib = req.query.bib;
    const timestamp = req.query.timestamp;

   const writeResult = await admin.firestore().collection('reads').add(
        {
            checkpoint: checkpoint,
            bib: bib,
            timestamp: timestamp,
        });

    res.json({result: `Read with ID: ${writeResult.id} added.`});

});
