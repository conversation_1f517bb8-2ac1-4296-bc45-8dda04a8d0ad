<?php

namespace App\Http\Livewire;

use Livewire\Component;

use App\Models\Vivos;
use App\Models\Eventos;
use App\Models\Carreras;
use App\Models\Categorias;
use App\Models\Etapas;

class StreamingVivo extends Component
{
    protected $vivos = [];
    public $idevento;
    public $idcarrera;
    public $idcategoria;
    public $idetapa;
    public $cantidad = 5;

    public function render()
    {
        if ($this->idcategoria) {
            $where = ['idcategoria' => $this->idcategoria];
            $categoria = Categorias::where(['idcategoria' => $this->idcategoria])->firstOrFail();
            $nombre = $categoria->nombre;
        } else if ($this->idetapa) {
            $where = ['idetapa' => $this->idetapa];
            $etapa = Etapas::where(['idetapa' => $this->idetapa])->firstOrFail();
            $nombre = $etapa->nombre;
        } else if ($this->idcarrera) {
            $where = ['idcarrera' => $this->idcarrera];
            $carrera = Carreras::where(['idcarrera' => $this->idcarrera])->firstOrFail();
            $nombre = $carrera->nombre;
        } else {
            $where = ['idevento' => $this->idevento];
            $evento = Eventos::where(['idevento' => $this->idevento])->firstOrFail();
            $nombre = $evento->nombre;
        }

        $query = Vivos::where($where)
            ->whereRaw("EXISTS (SELECT idparticipante FROM participantes WHERE participantes.idevento = $this->idevento AND participantes.idparticipante = vivos.idparticipante AND participantes.estado != 'eliminado' AND estado_carrera = 'inrace')");

        // Agregar condición adicional solo para el evento 2927
        if (in_array($this->idevento, [2927])) {
            $query->whereRaw("EXISTS (SELECT dato FROM datosxparticipantes WHERE datosxparticipantes.idevento = $this->idevento AND datosxparticipantes.iddato = 'nacionalidad' AND datosxparticipantes.idinscripcion = (SELECT idinscripcion FROM participantes WHERE participantes.idparticipante = vivos.idparticipante AND participantes.idevento = $this->idevento LIMIT 1))");
        }

        $this->vivos = $query->orderBy('largada_carrera')
            ->limit($this->cantidad)
            ->get();
        return view('livewire.streaming-vivo', [
            'vivos' => $this->vivos,
            'nombre' => $nombre,
        ]);
    }

}
