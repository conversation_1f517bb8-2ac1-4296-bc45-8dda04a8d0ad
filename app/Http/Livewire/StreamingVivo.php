<?php

namespace App\Http\Livewire;

use Livewire\Component;
use DB;

use App\Models\Vivos;
use App\Models\Eventos;
use App\Models\Carreras;
use App\Models\Categorias;
use App\Models\Etapas;

class StreamingVivo extends Component
{
    protected $vivos = [];
    public $idevento;
    public $idcarrera;
    public $idcategoria;
    public $idetapa;
    public $cantidad = 5;

    public function render()
    {
        $config_vivo = DB::table('config_vivo')->where('idevento', '=', $this->idevento)->get()[0];
        if ($this->idevento == 2927 && $config_vivo->idcategoria != 0) {
            $where = ['idevento' => $this->idevento];
            $this->idcarrera = Categorias::where(['idcategoria' => $config_vivo->idcategoria])->firstOrFail()->idcarrera;
            $config_vivo->idcategoria = 0;
            $carrera = Carreras::where(['idcarrera' => $this->idcarrera])->firstOrFail();
            $nombre = $carrera->nombre;
        } else if ($this->idcategoria) {
            $where = ['idcategoria' => $this->idcategoria];
            $categoria = Categorias::where(['idcategoria' => $this->idcategoria])->firstOrFail();
            $nombre = $categoria->nombre;
        } else if ($this->idetapa) {
            $where = ['idetapa' => $this->idetapa];
            $etapa = Etapas::where(['idetapa' => $this->idetapa])->firstOrFail();
            $nombre = $etapa->nombre;
        } else if ($this->idcarrera) {
            $where = ['idcarrera' => $this->idcarrera];
            $carrera = Carreras::where(['idcarrera' => $this->idcarrera])->firstOrFail();
            $nombre = $carrera->nombre;
        } else {
            $where = ['idevento' => $this->idevento];
            $evento = Eventos::where(['idevento' => $this->idevento])->firstOrFail();
            $nombre = $evento->nombre;
        }

        $query = Vivos::where($where)
            ->whereRaw("EXISTS (SELECT idparticipante FROM participantes WHERE participantes.idevento = $this->idevento AND participantes.idparticipante = vivos.idparticipante AND participantes.estado != 'eliminado' AND estado_carrera = 'inrace')");

        // Agregar el campo nacionalidad solo para el evento 2927
        if (in_array($this->idevento, [2927])) {
            $query->selectRaw('vivos.*, (SELECT dato FROM datosxparticipantes WHERE datosxparticipantes.idevento = ? AND datosxparticipantes.iddato = "nacionalidad" AND datosxparticipantes.idinscripcion = (SELECT idinscripcion FROM participantes WHERE participantes.idparticipante = vivos.idparticipante AND participantes.idevento = ? LIMIT 1)) as nacionalidad', [$this->idevento, $this->idevento]);
        }

        $this->vivos = $query->orderBy('largada_carrera')
            ->limit($this->cantidad)
            ->get();
        return view('livewire.streaming-vivo', [
            'vivos' => $this->vivos,
            'nombre' => $nombre,
        ]);
    }

}
