<?php

namespace App\Http\Livewire;

use DB;
use Livewire\Component;

use App\Models\Resultados;
use App\Models\Eventos;
use App\Models\Carreras;
use App\Models\Categorias;
use App\Models\Etapas;
use App\Models\Participantes;

use App\Helpers\FuncionesComunes;

class StreamingMeta extends Component
{
    protected $vivos = [];
    public $idevento;
    public $idcarrera;
    public $idcategoria;
    public $idetapa;
    public $llegada;
    public $prellegada;
    public $cantidad;

    public function render()
    {
        $segundos_prellegada = 1800;
        $idcontrol_prellegada = !$this->prellegada ? 0
            : (DB::table('controles')
                ->where('codigo', '=', $this->prellegada)
                ->where('idevento', '=', $this->idevento)
                ->where('tipo', '=', 'pre-llegada')
                ->get()[0]->idcontrol ?? 0);
        $idcontrol_llegada = !$this->llegada ? 0
            : (DB::table('controles')
                ->where('codigo', '=', $this->llegada)
                ->where('idevento', '=', $this->idevento)
                ->get()[0]->idcontrol ?? 0);

        $config_vivo = DB::table('config_vivo')->where('idevento', '=', $this->idevento)->get()[0];
        $this->cantidad = $config_vivo->cantidad;
        if ($this->idevento == 2927 && $config_vivo->idcategoria != 0) {
            $where = ['idevento' => $this->idevento];
            $this->idcarrera = Categorias::where(['idcategoria' => $config_vivo->idcategoria])->firstOrFail()->idcarrera;
            $config_vivo->idcategoria = 0;
            $carrera = Carreras::where(['idcarrera' => $this->idcarrera])->firstOrFail();
            $nombre = $carrera->nombre;
        } else if ($this->idcategoria) {
            $where = ['idcategoria' => $this->idcategoria];
            $categoria = Categorias::where(['idcategoria' => $this->idcategoria])->firstOrFail();
            $nombre = $categoria->nombre;
        } else if ($config_vivo->idcategoria != 0) {
            $where = ['idcategoria' => $config_vivo->idcategoria];
            $categoria = Categorias::where(['idcategoria' => $config_vivo->idcategoria])->firstOrFail();
            $nombre = $categoria->nombre;
        } else if ($this->idetapa) {
            $where = ['idetapa' => $this->idetapa];
            $etapa = Etapas::where(['idetapa' => $this->idetapa])->firstOrFail();
            $nombre = $etapa->nombre;
        } else if ($this->idcarrera) {
            $where = ['idcarrera' => $this->idcarrera];
            $carrera = Carreras::where(['idcarrera' => $this->idcarrera])->firstOrFail();
            $nombre = $carrera->nombre;
        } else {
            $where = ['idevento' => $this->idevento];
            $evento = Eventos::where(['idevento' => $this->idevento])->firstOrFail();
            $nombre = $evento->nombre;
        }

        $query = Resultados::where($where)
            ->whereRaw("EXISTS
                (SELECT idparticipante FROM participantes
                    WHERE participantes.idevento = $this->idevento
                    AND participantes.idparticipante = resultados.idparticipante
                    AND participantes.estado != 'eliminado'
                    AND participantes.estado_carrera = 'inrace'
                    AND participantes.nombre != ''
                    AND participantes.idcategoria > 0
                )")
            ->where('updated_at', '>', now()->subSeconds($segundos_prellegada))
            ->orderByDesc('updated_at');

        if (in_array($this->idevento, [2927])) {
            $query->selectRaw('resultados.*, (SELECT dato FROM datosxparticipantes WHERE datosxparticipantes.idevento = ? AND datosxparticipantes.iddato = "nacionalidad" AND datosxparticipantes.idinscripcion = (SELECT idinscripcion FROM participantes WHERE participantes.idparticipante = resultados.idparticipante AND participantes.idevento = ? LIMIT 1)) as nacionalidad', [$this->idevento, $this->idevento]);
        }
        $terminados_todos = $query->limit($this->cantidad)
            ->get();

        $this->terminados = $this->eliminarRepetidos($terminados_todos);
        $terminados = $this->prepararTiempos($terminados_todos);
        // $terminados = $terminados_todos;

        if ($idcontrol_prellegada && $idcontrol_llegada) {
            $prellegados = Participantes::where($where)
                ->select("*")
                ->selectRaw('(SELECT COALESCE((SELECT idlectura FROM lecturas WHERE lecturas.idparticipante = participantes.idparticipante AND lecturas.idcontrol = '.$idcontrol_prellegada.' AND lecturas.estado = "ok" ORDER BY tiempo DESC LIMIT 1), 0)) AS idlectura_ultima')
                ->selectRaw('(SELECT COALESCE((SELECT vuelta FROM lecturas WHERE lecturas.idparticipante = participantes.idparticipante AND lecturas.idcontrol = '.$idcontrol_llegada.' AND lecturas.estado = "ok" ORDER BY tiempo DESC LIMIT 1), 0)) AS vuelta')
                ->whereRaw('EXISTS (SELECT idlectura FROM lecturas WHERE idcontrol = '.$idcontrol_prellegada.' AND lecturas.idparticipante = participantes.idparticipante AND estado = "ok" AND tiempo >= "'.date('Y-m-d H:i:s', time() - $segundos_prellegada).'")')
                ->whereRaw('NOT EXISTS (SELECT idlectura FROM lecturas WHERE idcontrol = '.$idcontrol_llegada.' AND lecturas.idparticipante = participantes.idparticipante AND estado = "ok" AND tiempo >= "'.date('Y-m-d H:i:s', time() - $segundos_prellegada).'")')
                ->whereRaw('participantes.idparticipante > 0')
                ->whereRaw('participantes.estado != "eliminado"')
                ->whereRaw('participantes.estado_carrera = "inrace"')
                ->whereRaw('participantes.nombre != ""')
                ->whereRaw('participantes.idcategoria > 0')
                ->orderBy('idlectura_ultima')
                ->get();
        } else {
            $prellegados = null;
        }

        return view('livewire.streaming-meta', [
            'terminados' => $terminados ?? [],
            'prellegados' => $prellegados ?? [],
            'nombre' => $nombre,
        ]);
    }

    private function eliminarRepetidos($terminados)
    {
        $repetidos = [];
        $unicos = [];
        foreach ($terminados as $key => $terminado) {
            if (!in_array($terminado->idparticipante, $repetidos) && count($unicos) < $this->cantidad) {
                $repetidos[] = $terminado->idparticipante;
                $unicos[] = $terminado;
            }
        }

        return $unicos;
    }

    private function prepararTiempos($terminados)
    {
        $terminados = $terminados->map(function ($terminado) {
            $tiempo_listo = FuncionesComunes::convertirFinal($terminado['tiempo_carrera'] / 1000);
            $terminado->tiempo_listo = $tiempo_listo;
            return $terminado;
        });

        return $terminados;
    }

}
