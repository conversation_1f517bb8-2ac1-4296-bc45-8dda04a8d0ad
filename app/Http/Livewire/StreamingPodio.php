<?php

namespace App\Http\Livewire;

use DB;
use Livewire\Component;

use App\Models\Resultados;
use App\Models\Eventos;
use App\Models\Carreras;
use App\Models\Categorias;
use App\Models\Etapas;

use App\Helpers\FuncionesComunes;

class StreamingPodio extends Component
{
    protected $terminados = [];
    public $idevento;
    public $idcategoria;
    public $idetapa;
    public $idcarrera;
    public $cantidad;
    public $desde;

    public function render()
    {
        $config_vivo = DB::table('config_vivo')->where('idevento', '=', $this->idevento)->get()[0];
        if ($this->idevento == 2927 && $config_vivo->idcategoria != 0) {
            $where = ['idevento' => $this->idevento];
            $this->idcarrera = Categorias::where(['idcategoria' => $config_vivo->idcategoria])->firstOrFail()->idcarrera;
            $config_vivo->idcategoria = 0;
            $carrera = Carreras::where(['idcarrera' => $this->idcarrera])->firstOrFail();
            $nombre = $carrera->nombre;
        } else if ($this->idcategoria) {
            $where = ['idcategoria' => $this->idcategoria];
            $categoria = Categorias::where(['idcategoria' => $this->idcategoria])->firstOrFail();
            $nombre = $categoria->nombre;
        } else if ($config_vivo->idcategoria != 0) {
            $where = ['idcategoria' => $config_vivo->idcategoria];
            $categoria = Categorias::where(['idcategoria' => $config_vivo->idcategoria])->firstOrFail();
            $nombre = $categoria->nombre;
        } else if ($this->idetapa) {
            $where = ['idetapa' => $this->idetapa];
            $etapa = Etapas::where(['idetapa' => $this->idetapa])->firstOrFail();
            $nombre = $etapa->nombre;
        } else if ($this->idcarrera) {
            $where = ['idcarrera' => $this->idcarrera];
            $carrera = Carreras::where(['idcarrera' => $this->idcarrera])->firstOrFail();
            $nombre = $carrera->nombre;
        } else {
            $where = ['idevento' => $this->idevento];
            $evento = Eventos::where(['idevento' => $this->idevento])->firstOrFail();
            $nombre = $evento->nombre;
        }

        $this->desde = (!$this->desde || $this->desde == 0) ? 1 : $this->desde;
        $terminados_todos = Resultados::where($where)
            ->whereRaw("EXISTS (SELECT idparticipante FROM participantes WHERE participantes.idevento = $this->idevento AND participantes.idparticipante = resultados.idparticipante AND estado_carrera = 'inrace')")
            ->orderBy('tiempo_carrera')
            ->offset($this->desde - 1)
            ->limit($this->cantidad ?? $config_vivo->cantidad)
            ->get();
        $this->terminados = $this->eliminarRepetidos($terminados_todos);
        // $this->terminados = $terminados_todos;

        return view('livewire.streaming-podio', [
            'terminados' => $this->terminados,
            'nombre' => $nombre,
        ]);
    }

    private function eliminarRepetidos($terminados)
    {
        $repetidos = [];
        $unicos = [];
        foreach ($terminados as $key => $terminado) {
            if (!in_array($terminado->idparticipante, $repetidos)) {
                $repetidos[] = $terminado->idparticipante;
                $terminado->tiempo_carrera = FuncionesComunes::convertirFinal($terminado->tiempo_carrera/1000);
                $unicos[] = $terminado;
            }
        }

        return $unicos;
    }

}
