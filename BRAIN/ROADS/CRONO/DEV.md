# 🥇 CRONO > DEV
-------------------------------------------------------------------------------

# DOING 📆

- https://cronometraje-instantaneo.sentry.io/issues/6765503217/?referrer=alert_email&alert_type=email&alert_timestamp=1753282699575&alert_rule_id=11588895&notification_uuid=2690da1d-75cf-4b64-a77f-30389e90b59b&environment=production


- [ ] Adjuntar comprobante de pago #310: poder adjuntar comprobante de pago
- [ ] Pixels de MKT #306: poder agregar pixels de MKT a los eventos
  - Issue y listar pixel de Meta para Lucas (https://mail.google.com/mail/u/0/#inbox/********************************)
  - Track inscripciones, generar issue con los 2 notas en Readly
  - https://raceid.com/organizer/event-marketing/how-to-track-where-your-registrations-come-from/
  - https://raceid.com/organizer/event-marketing/track-registration-data-campaign-links/?utm_source=pocket_saves

- [ ] Transferencias Inteligentes #303: poder transferir dinero a un evento
- [ ] FILAMENT Partidores #308: Agregar compatibilidad para los equipos que vamos a utilizar en Calafate
- [ ] Pasar issue del pago de eventos
  - Me da mal la caja con el pago aprobado (https://alfa.saasargentina.com/clientes.php?a=ver&id=250)
  - Reseteo el evento porque estaba terminado pero sin fecha de terminado (revisar que no pase)
  - No me llega el botón de pago para el evento de Moxi 2718
  - Cerrar eventos viejos y pasar deudores a Juli
- [ ] Issue para convertir caja Santander a pesos
- [ ] Informe NAF para Omar Chile


- [ ] Tengo los logos de deportes
- [ ] Sumar puntos a select
- [ ] Nombre y DNI en Panel de Equipos
- [ ] Armar nombre del equipo desde apellidos
- [ ] Volver a probar de agregar las categorías por edad, pero esta vez guardando la configuración entre
  - Año cumplido el día del evento: los que cumplen años hasta el día del evento, se los considera dentro de esa categoría, y los que cumplen años el día posterior ya es la siguiente categoría.
  - Año calendario: los que cumplen años dentro de este año, incluyendo hasta el 31 de diciembre, pertenecen a esa categoría.
- [ ] https://www.youtube.com/watch?v=SeeGkxEnnfg Sentry se conecta a Augment




## ACTUALIZAR CON DEPLOY

https://www.mercadopago.com.ar/developers/es/news/2025/05/30/Mercado-Pago-MCP-Server--Accelerate-Your-Integrations-with-Our-AI
https://laravel-news.com/laravel-claude-code-setup?utm_source=newsletter&utm_medium=email&utm_content=weekly&utm_campaign=573&bento_uuid=c6d12c8f-1108-494e-963f-96ecf478d577

## Novedades y MKT
  - Multicategoría
  - Aviso de revisión de chips (y recordar las otras revisiones)
  - 3 usuarios por evento (a newsletter)
  - Los chips ahora son de los cronometradores
  - Controlar chip
  - desentregar en podio
  - % chip en informe pc
  - Clasificacion
  - Agregar apellido al vivo
  - Cambiar el informe para el seguro, que en lugar del DNI, utilice el Dato Único así sirve para todos los países. Poner también el Whatsapp contatenado al celular en el informe de Seguridad
  - Todo lo del nuevo Filament (por ahora sólo a usuarios cronometradores)

- puntaje que es un enum('','generales','categorias','sexo','multiplicador-segundos-sexo')
- puntos que es un campo de Texto
- suma_puntos que es un varchar(20)
- auto_numerar_desde que es un campo numérico positivo int(10)
- auto_asignar_tag que es un varchar(8)

- distancia que es un número entero
- velocidad_promedio que es el mismo enum que en config_cronometraje
- tiempo_minimo que es un número entero
- tiempo_maximo que es un número entero
- tiempo_ideal que es un número entero

https://nightwatch.laravel.com/docs/getting-started/start-guide

## ACTUALIZAR FILAMENT

- [x] Instalar MPCs
- [ ] Configurar Rules
- [x] Ver que plugins pueden fallar
- [ ] Desinstalar https://filamentphp.com/plugins/jeffgreco-breezy
- [ ] Estudiar como actualizar Filament a v4
- [ ] Actualizar Laravel 12


## ORDENAR PARA CURSOS O CAPACITACIONES

- Generador de archivos html guardado dentro de beta
- Agregar panel, carga de archivos, siguiente y atrás automáticos
- Prueba en
- Vídeo con cámara en altura para cursos
- Pasarlos adentro del sistema (solo para usuarios)
- Vídeo explicando la terminología (que son lecturas, tiempos, resultados, etapas, inscripto, acreditado, etc.)
- Sector de preguntas frecuentes en cada curso, pasar los vídeos a textos con IA y poner un buscador
- Controlar repetidos y controlar velocidades promedio
- Responsable de imágen
- https://www.ionos.es/digitalguide/paginas-web/diseno-web/que-es-css-un-tutorial-con-lo-que-necesitas-saber/
- https://laraveldaily.com/post/filament-infolist-create-custom-components-tailwind-css
- La estructura va a ser menú Capacitaciones con las siguientes páginas: Grupo de Whatsapp, Cursos, Preguntas Frecuentes, Soporte en Evento, Novedades, Blog Anterior

## ORDENAR

- [ ] Convertir los widgets y agregar varios relojes para OCR y Rally
- [ ] Me faltan vídeos de puntos, velocidad promedio y nuevos métodos de tiempo (lo de rally y tiempos mínimos)
- [ ] Terminar auto asignar chips
- [ ] Revisar lo de Zona que está en la carpeta DEV porque no da igual el resultado con el nuevo generales
- [ ] Tengo feedback en el tema de eventos en Google Search Console
- [ ] https://github.com/kirschbaum-development/laravel-loop?__readwiseLocation=
- [ ] En Stich de Google podemos hacer una nueva interfaz para la nueva app para Participantes
- [ ] Pasar lo de el nuevo auto-numerar al issue de transferencias
- [ ] Me cargó 72 en la caja en lugar de 70 (https://scripts.saasargentina.com/?script=1&iue=MTVvtWMaVPdEGDpcsyU3&api_secret=i8eOimYHPv9bjPzF8de7q2OaSG2d0UYq&a=nuevo_pago_completo&idservicio=2699&idcliente=257&nombre=SMART%20TIMING%20VENEZUELA&mail=<EMAIL>&fecha=2025-05-18%2000:00:00&producto=cronometrador&precio=80&descuento=10&total=72&moneda=2&pago=70&formapago=crypto&observacion=pago%20realizado%20por%20binance%20y%20notificado%20al%20jefe%20Andres.)

- [ ] Escribir mejoras para Francisco para poder gestionar cobranzas (pensar también en los cronometradores)
- [ ] Quiero poder usar teclado
- [ ] El importador no es multichip, probar con el archivo de Fenix
- [ ] Script para terminar eventos con cron
- [ ] Controles con errores
- [ ] Mejorar los relojes
- [ ] Ver si puedo activar kiosko de consulta
- [ ] A Colombia le aparecen todos los usuarios, organizadores y cronometradore al editar evento

- [ ] Ordenar futuros módulos:
  - Módulo para locures
  - Módulo para vivos
  - Módulo para seguimiento satelital gps
  - Abrir modal con descripción cuando está desactivado, sería ideal un vídeo
  - Lista YouTube de tecnologías
  - Lista de módulos más adelante
  - Curso de Cronometraje abierto con lista en YouTube
  - Intercambio de tiempos y generar tiempo intermedio

- [ ] [Seguridad Critical OpenSSH vulnerability – Consola de Google Cloud](https://console.cloud.google.com/security/advisorynotifications/details/projects/211589375263/global/02384641-db43-4895-8ff7-8daee94ab77b?project=cronometrajeinstantaneo-151420&pli=1)
- [ ] Análisis de seguridad  https://v1.web-check.xyz/results/https%3A%2F%2Fcronometrajeinstantaneo.com
- [ ] Empezar a escribir [DOC.md](https://DOC.md)
- [ ] https://speedhive.mylaps.com/app


## ERRORES

- [ ] Revisar el error de Rally Bs.As.
- [ ] Ver que los códigos de las organizaciones se generen y actualicen bien (correr un comando para arreglar todos). Ver también que no se puedan repetir.
- [ ] De alguna forma se está pudiendo repetir los nombres de los eventos.


## MEJORAS MENORES VARIAS

- [ ] Limitar el vivo

- [ ] Más fórmulas de puntos para APE: Punto por vuelta dada, punto de presentismo y si supera o no la mitad de carrera
- [ ] Agregar enlace al botón de pago
  - https://cronometrajeinstantaneo.com/inscripciones/open-shimano-latam-1-cerro-bayo-2025/Yk9FNlRIQVVQbytMT1dSY0FYcTgrWGdiZVhTdlpSYUpka3V2TjBRTlFPUTIzandudXZUbG1Db2NzU052Z2pBUw%3D%3D
- [ ] Necesito un re-importador desde JSONs por si resetean el evento
- [ ] Hay que poner un backup al resetear eventos
- [ ] Crear categorías al importar
- [ ] Ver si se puede poner cantidad tipo podio a las generales por sexo
- [ ] Avisar con alguna marca en los resultados y/o en el modificador de tiempos, que la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión
- [ ] Ordenar llegada según timer
- [ ] Nombre de equipo desde apellidos (lo pidió Quatro Vientos)
- [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)
- [ ] Largada y llegada mismo código
- [ ] Largada por categorías para Quatro Vientos
- [ ] Ver si se puede poner una carrera gratis con una pasarela de pago sin precio
- [ ] En el evento Vuelta Ciclística a Ibarra 2024 dio tiempos negativos, o sea que en largadas individuales puede estar viendose los negativos, que no deberían
- [ ] Agregar código de identificación en los MP como pidió Kittu
- [ ] No acredita versión 3
- [ ] En el nuevo importar, consultar inconsistencias de categorías al importar participantes
- [ ] Debería facturar automáticamente por ARCA en ARG y por la LLC en el exterior
- [ ] Auto categorizacion al cambiar fecha desde y hasta
- [ ] Entran miles de visitas por día, hay que optimizar la carga de los resultados desde html
- [ ] Puedes hacer una herramienta de ordenar columnas para crono y nombre de las columnas. Y activado o desactivado. Puede quedar algo bastante fácil y después eso armás un array y lo procesás en todos los resultados.
- [ ] Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)
- [ ] Poder importar a SaaS el listado de participantes de Crono, poner un valor y un producto y que te genere todas las facturas (en un futuro que lo mande por mail)
- [ ] https://www.honeybadger.io/tour/logging-observability/?utm_source=laravelnews&utm_medium=paid&utm_campaign=2024-10-21-laravelnews-sponsored-post


## ISSUE CRONOMETRADORES

- [ ] Agregar a los eventos estado_cronometrador y obs_cronometrador
- [ ] Poder importar presupuestos, contratos, etc. (modo para que lo vea el organizador o no)


## ISSUE ORGANIZADOR

- Módulo para premiación: Ordenar textos, sorteos y generales en el medio del podio
- Módulo para staff y gestión de tareas
- Caja con salidas y entradas de dinero
- Ver que hay eventos del tipo entrenamiento


## HARDWARE

- [ ] Error en reader que cuelga y/o muestra mensaje
- [ ] Nuevos Readers Invelion Wifi
- [ ] Zebra 🙏
- [ ] Me faltan 2 modelos de Chafon
- [ ] Rufus funciona con marcas como Zebra, Impinj y Chainway.
- [ ] Reloj partidor
- [ ] Recuperar Raspberry Pi
- [ ] Probar pistola RFID con nuevos softwares
- [ ] Revisar conexión de fotocélulas a Windows con adaptador
- [ ] Eliminar llave SSH de Crono
- [ ] Probar whisper para cronometrar


-------------------------------------------------------------------------------

# MILESTONES DEV

**Actualizar framework y mejorar diseño**
MS: el equipo de Crono pueda gestionar todo. Todavía no entran usuarios finales

- [x] FILAMENT Roles #154: Definimos ya la realidad de los usuarios, que funcione en FILAMENT, pero mantener compatibilidad con admin
- [x] FILAMENT Gestión Eventos y Usuarios #294 y #193: poder ver y modificar eventos y usuarios (la intensión es matar el historial pero todavía no la alta)
- [x] FILAMENT Estados #104: Permite gestionar mejor lo pendiente (pendiente, iniciado, terminado, postergado (sin fecha), cancelado) y muestra la información que va a salir en /eventos
- [x] FILAMENT Cuentas y saldos #299: agregar tabla descuentos, pagos adelantados, tipos de eventos y precios para la gestión completa por Juli

**Generar sector de Eventos en el sitio**
MS: Script para generar páginas estáticas de eventos

- [x] EVENTOS Listado #46: Empezamos a tener el script y el listado de eventos

**Mejorar incripciones**
MS: Agregar funcionalidades para mejorar el tema cobranzas

- [ ] Adjuntar comprobante de pago #310: poder adjuntar comprobante de pago
- [ ] Transferencias Inteligentes #303: poder transferir dinero a un evento
- [ ] Pixels de MKT #306: poder agregar pixels de MKT a los eventos
- [ ] PAGOS Oauth MP #307: Terminar Oauth de MercadoLibre https://www.mercadopago.com.ar/developers/es/docs/mcp-server/overview
- [ ] PAGOS PSE Colombia #312: Agregar pago con PSE
- [ ] PAGOS Flow Chile: Agregar pago con Flow
- [ ] PAGOS Adicionales: Poder agregar adicionales a los pagos
- [ ] INSCRIPCIONES Talles: Agregar control de stock de talles y otros datos relacionados

**Compatibilidad Partidores**
MS: Agregar compatibilidad para los equipos que vamos a utilizar en Calafate

- [ ] FILAMENT Partidores #308: Agregar compatibilidad para los equipos que vamos a utilizar en Calafate

**Actualizar Filament**
MS: Actualizar Filament a v4 y Laravel a v12 para mejorar la velocidad y empezar a agregar funcionalidades

- [ ] FILAMENT Actualizar #309: Actualizar Filament a v4 y Laravel a v12
- Laravel Nightwatch
- [ ] FILAMENT Mejoras varios #302: Evaluar que mejoras meter

**Automatizar cuentas**
MS: Reemplazar el Historial y automatizar pagos. Entran usuarios finales y tienen control total de sus eventos y datos.

- [ ] Transferencias Inteligentes a nosotros, auto-facturación, carga de varios eventos adelantados, compra de promociones, caja en pesos
- [ ] FILAMENT Alta #263: permitimos que los usuarios nuevos generen eventos (Sin ver el tema pagos todavía)
- [ ] FILAMENT Terminar Login #120: poder loguearse, generar usuarios y cambiar contraseñas
- [ ] Mail a los deudores y a Juli informando

**Sitio a MD**
MS: Pasar todo el sitio y la información a MD para poder actualizar todo rápido y posicionarnos para AI

- [ ] SITIO desde MD #290: Pasar los cursos a md y que los procese el mismo script de eventos
- [ ] NOVEDADES y CURSOS: Pasar las novedades a md y al mismo script (actualizar todo lo desarrollado)
- [ ] EVENTOS Terminar Listados #311: Terminar las mejoras en el script de eventos

**Terminar funcionalidades: vídeos, puntos, velocidad promedio, datos extras de archivos**
MS: No configurar funcionalidades manualmente yo

- [ ] FILAMENT Admin y Módulos #276: poder generar el acceso al nuevo módulo de eventos
- [ ] Limpiar los issues de features. UTILIZAR ACCIONES PARA TODA LA LÓGICA DE NEGOCIOS PENSANDO EN AI!
- [ ] Edades en las categorías #236: Hacer más fácil la configuración de las categorías
- [ ] DEPORTES #165: generar sistemas para cada deporte
- [ ] CHIPS #298: dejar de gestionar chips a mano

**Crono sin errores**
- Panel del participante con vídeo y tiempos
- Modificar tiempos nuevo
- Logs de TODO para recuperar información y para que lo vea el responsable

---

**Hardware**
MS: agregar más opciones de readers, fotocélulas y otros equipos. Ver si ya metemos nueva interfaz y sincro automática

**Terminar Nuevas Inscripciones**

MS: Potenciar las inscripciones a la altura del mercado

- [ ] Terminar Oauth MP y generar documentación y MKT #264
- [ ] Agregar pagos Colombia (crear issue dividiendo #272)
- [ ] Ver si podemos poner las fotos en los tickets #292
- [ ] Pagos Precios #264: Terminar una primera etapa con el botón y lo hecho actualmente

**Generar ventanas para la creación de Micrositios (sólo para usuarios al principio)**

- SITIOS: Por ahora listar eventos (QR Ecuador)


**Métricas, Dashboard y KPIs automáticos con AI 📈**

MS: tener un dashboard con las métricas más importantes automatizadas

- PAGOS Descuentos y Adicionales #268: poder ofrecer descuentos adicionales
- MÉTRICAS Automáticas con AI #279: poder ver por fin las métricas en Filament u afuera con AI


### SIGUEN

- FILAMENT Pagos #227: todo lo de las pasarelas para que paguen automáticamente
- Métricas y KPIs automáticos con AI 📈
- Automatización y conexión con AI
- Agregar una función de Kiosko en la app
- Agregar compatibilidad para los equipos: Macsha xFTP y probar wireshark
- Agregar compatibilidad para Chafón x2
- Migrar base de datos relacional a Cloud MySQL
- Definí usar Cloud MySQL para los eventos y participantes. Luego para las lecturas y resultados, pasar a Firestore
- Generar Cloud Run con Containers
- Generar los procesos en Cloud Functions para los datos estáticos
- Generar los procesos para exportar los resultados a CDN
- Lograr primera etapa de CronoChat para la gestión de eventos y micrositios
- Desarrollar toda la gestión de pagos dentro de CronoChat
- Re-programar todo lo de los chips en la versión de la APP 3.x
- Conectar CronoChat a Whatsapp
- Lograr segunda etapa de CronoChat para las inscripciones de participantes y sus pagos
- Empezar con pruebas reales con Beacons
- Agregar un sector para administrar Chips y Beacons
- Agregar compatibilidad para: Pistola RFID, Chips Activos
- Generar sector de cada Hardware en el sitio

### IDEAS MENORES

Hay un listado de varias ideas sueltas para ordenar en [DEV Ideas Menores](./DEV%20Ideas%20Menores.md).

### PEDIDOS PUNTUALES DE CLIENTES

- OWA: Conectar Crono con SaaS (tener en cuenta que quiere tener distintos CUITs y distintos eventos facturarlos con esos CUITs)
