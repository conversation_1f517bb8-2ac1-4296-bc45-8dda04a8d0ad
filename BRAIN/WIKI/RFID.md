********************************************************************************
  DESARROLLO
********************************************************************************
Ideas para próxima antena:
  - Buen seguimiento de los chips por idorganizacion
  - Agregar tipo de lectura en la tabla lecturas, para hacer los porcentajes de lecturas de chips y jugar con esas estadísticas
  - Falta tags en los equipos
  - Para hacer el sistema multi-tag, tengo que meter un bucle en parti_manual y agregar más inputs
  - Probar distintos tipos de lectura según buffer
  - Mostrar el código del tag en lugar del EPC cuando lo tengamos
  - Botón para prender o apagar la lectura
  - Agregar una barra de monitoreo
    - Copiar info desde apps chinas
    - Cantidad de lecturas por antena
    - Temperatura de la antena
    - Firmware de la antena
    - Lecturas por segundo y esos valores
    - Cantidad de chips no reconocidos como participantes
  - Agregar configuraciones posibles:
    - Radio spectrum specification
    - Output power
    - Cantidad de antenas (auto-detección)
    - Tiempo de rechazo de tags
  - Ventana de auto-consulta para entregar chips, con envío de ticket digital por mail
  - Falta una forma de revisar que chips fueron devueltos


********************************************************************************
  COMUNICACIÓN HEXA
********************************************************************************
- Reset reader: A0 03 01 70 EC / No responde
- Beep apagado: A0 04 01 7A 00 E1 y responde A0 04 01 7A 10 D1
- Beep por inventory A0 04 01 7A 01 E0 y responde A0 04 01 7A 10 D1
- Beep por detección A0 04 01 7A 02 DF y responde A0 04 01 7A 10 D1
- Get RF Output Power A0 03 01 77 E5 y responde A0 04 01 77 1E C6 para 30 dBm / A0 04 01 77 1C C8 para 28 dBm / A0 04 01 77 01 E3 para 1 dBm / A0 04 01 77 00 E3 para 0 dBm / A0 04 01 76 3C A9 a 60 dBm
- Set RF Output Power a 30 dBm A0 04 01 76 1E C7 y responde A0 04 01 76 10 D5
- Set RF Output Power a 29 dBm A0 04 01 76 1D C8 y responde A0 04 01 76 10 D5
- Set RF Output Power a 28 dBm A0 04 01 76 1C C9 y responde A0 04 01 76 10 D5
- Set RF Output Power a 1 dBm A0 04 01 76 01 E4 y responde A0 04 01 76 10 D5
- Se puede setear RF Output Power hasta 33 dBm como máximo con A0 04 01 76 21 E4 y con los siguientes códigos para set
A0 04 01 76 01 E4
A0 04 01 76 02 E3
A0 04 01 76 03 E2
A0 04 01 76 04 E1
A0 04 01 76 05 E0
A0 04 01 76 06 DF
A0 04 01 76 07 DE
A0 04 01 76 08 DD
A0 04 01 76 09 DC
A0 04 01 76 0A DB
A0 04 01 76 0B DA
A0 04 01 76 0C D9
A0 04 01 76 0D D8
A0 04 01 76 0E D7
A0 04 01 76 0F D6
A0 04 01 76 10 D5
A0 04 01 76 11 D4
A0 04 01 76 12 D3
A0 04 01 76 13 D2
A0 04 01 76 14 D1
A0 04 01 76 15 D0
A0 04 01 76 16 CF
A0 04 01 76 17 CE
A0 04 01 76 18 CD
A0 04 01 76 19 CC
A0 04 01 76 1A CB
A0 04 01 76 1B CA
A0 04 01 76 1C C9
A0 04 01 76 1D C8
A0 04 01 76 1E C7
A0 04 01 76 1F C6
A0 04 01 76 20 C5
A0 04 01 76 21 C4

EJEMPLOS DE COMO LLEGAN EPCS:
A0 13 01 89 9C (34 00 -PC) (E2 80 11 70 00 00 02 0C 21 9E 11 93 -EPC) 38 67 (-88dBm / Carrier frecuency 922)
A0 13 01 89 9C (34 00 -PC) (E2 80 11 70 00 00 02 0C 21 9E 11 83 -EPC) 52 5D (-48dBm / Carrier frecuency 918)
A0 13 01 89 B8 (30 00 -PC) (E2 80 68 90 00 00 00 00 A0 6F 1D 79 -EPC) 37 A5 (-79dBm / Carrier frecuency 926)
A0 13 01 89 B8 (34 00 -PC) (E2 80 11 70 00 00 02 0C 21 9E 11 83 -EPC) 4A 49 (-62dBm / Carrier frecuency 926)
A0 13 01 89 B8 (34 00 -PC) (E2 80 11 70 00 00 02 0C 21 9E 11 93 -EPC) 38 4B (-67dBm / Carrier frecuency 918)

EJEMPLO DE COMO LLEGA CON PC 14:
RA00B0189201400156803E95DD1
A0 0B 01 89 58  14 00  15 68 03 E9 55 A1

EPSs comprados en 2025: 000000000000000000000400
EPSs comprados recuperables: E2801191A5030060ACB9F57D


********************************************************************************
  NOTA PARA EL BLOG
********************************************************************************

## DIFERENCIAS ENTRE REALTIME Y FAST SWITCH

- En Realtime el reader lee una sola antena a la vez, entrega todo lo leído al software y recién ahí pasa a la próxima antena. Esta opción es recomendable cuando los participantes pasan más lentos y/o hay mucha cantidad de participantes pasando por el mismo puesto de lectura.
- En Fast Switch el reader lee una antena y pasa a la siguiente, mientras un segundo CPU dentro del reader va enviando todo lo leído sin parar. Esta opción es recomendable cuando son pocos participantes y/o pasan muy rápido por el puesto de lectura.
- Los readers Chafón no tienen Fast Switch, solo Realtime, por lo que ambas opciones son iguales en estos readers.

## Agregar videos

En algunos eventos es mayor la necesidad de automatizar el cronometraje y para eso no hay nada mejor que usar chips. Delegamos la toma de tiempos a la conocida tecnología RFID (*** COMPLETAR NOMBRE Y ENLACE A WIKIPEDIA). Para eso contamos con un *reader* (lector encargado de enviar, recibir y procesar las señales), las antenas (que las hay de varios tipos y formas), los tags (comunmente llamados chips en el mundo del cronometraje) y el software o programa para gestionar las lecturas. La aplicación de Cronometraje Instantaneo para Windows es completamente compatible con esta tecnología.

Una de las principales ventajas de utilizar cronometraje con tecnología RFID es la posibilidad de tomar los tiempos de varios participantes pasando al mismo tiempo por nuestro punto de control, algo que se torna difícil cuando se cronometra con cualquier otra tecnología. Otra de las ventajas es la comodidad durante el evento, ya que el trabajo del cronometrador es un poco más relajado con la ayuda de sus chips.

Pero necesitamos conocer bien esta tecnología antes de saber si es la adecuada para nuestro evento. Esta tecnología fue inventada *** BUSCAR HISTORIA Y REFERENCIAS *** y es ampliamente usada en las industria de la logística, la seguridad *** BUSCAR EJEMPLOS ***.

*** CHECKEAR TODO ESTE PÁRRAFO *** Técnicamente el reader genera una señal de radio frecuencia y la envía por las antenas. Los chips reciben la señal y devuelven

Tipos de chips: pasivos y activos

---CONTRAS---
- Porcentaje de lecturas: depende del tipo de antenas y su ubicación (más info abajo)
- Costos (más que utilizar la app o tocoelula)
- Tiempo de instalación
- Entrega y devolución de chips


- Tipos de Readers y antenas
- Crono es compatible con genéricas (es el único sistema abierto de latinoamerica)
- Donde comprar (ver para tu país específico o en China)

- Compatible con cualquier marca de antena y cualquier marca de chips (único sistema totalmente abierto de latinoamerica)
- Nombre del driver: Silicon Labs CP210x USB to UART Bridge
- Para usar USB tener prendidas las llaves 7 y 8. Para red las 3 y 4.
- La IP de las antenas es IP address: ************* / Net mask: ************* / Port number: 4001
- Modelo del reader de Gaby: Invelion YR8700 8 channel UHF RFID reader
- Modelo del reader de Esquel: Invelion YR8600 4 channel UHF RFID reader
- Modelo de las antenas de Esquel: Invelion- YR9028 (Frequency: 860-960 mhz / Gain: 9.2dBi / VSWR: 1.3)

## TIPOS DE CHIPS

- M750 dogbones
- RP6 chips
- NXP U9

*Recuperables*
- Blue HuTag XC-2 USD2.50 each
- Blue HuTag XC-3 USD3.50 each


## RECOMENDACIONES DE EQUIPOS RUFUS

🏃🏃🏃 Puntos de Cronometraje de Alta Densidad: líneas de inicio y meta para eventos de gran tamaño.

- Zebra FX9600: Diseñado para entornos de alta densidad, el FX9600 es ideal para líneas de salida y meta con grandes multitudes. Con su diseño robusto y alta tasa de lectura, es perfecto para puntos de control en carreras concurridas. Disponible en modelos de 4 y 8 puertos de antena.
- Impinj R420: El R420 ofrece una solución potente -y más costosa- para eventos con altos volúmenes de participantes, con 4 puertos de antena y tasas de lectura altas, incluso en entornos con ruido o interferencia de RF. 

🏃🏃 Puntos de Cronometraje de Densidad Media: puntos de control intermedios o carreras de tamaño medio.

- Zebra FX9500: Un clásico confiable para lecturas en puntos de control en eventos de tamaño medio, equilibrando rendimiento y precio. Es un modelo descontinuado, pero aún se encuentra disponible en el mercado a precios accesibles.
- Chainway UR4: Una opción económica y versátil, ideal para carreras con volúmenes moderados de participantes. Cuenta con 4 puertos de antena y utiliza el chip RFID Impinj E710, siendo resistente a interferencias electromagnéticas y eficiente en la dispersión de calor.
 
🏃 Puntos de Cronometraje de Baja Densidad: carreras de menor tamaño.

- Impinj R220: Eficiente y confiable para eventos pequeños, el R220 gestiona menos tags, pero ofrece lecturas precisas con sus 2 puertos de antena y protocolos anti interferencias.
- Chainway UR4: Versátil y económico, perfecto para puntos de control de baja densidad sin comprometer la fiabilidad, con una tasa de lectura de más de 900 tags/seg.


### CABLES

Here are the two connectors for the standard antennas and RFID readers:
- RP-TNC male (for reader)
- N-male (for antenna)

Make sure to purchase the right connector size for your cable type (LMR 195/LMR 240/LMR 400). They are different for each size of cable. Here are the standard cables. The larger the cable the lower the losses to the antenna.
- LMR 195 11.1 dB loss per 100 ft @900 mHz (this is the cheaper, thin and flexible cable but has the highest losses.)
- LMR 240 7.6 dB loss per 100 ft @ 900 mHz
- LMR 400 3.9 dB loss per 100 ft @ 900 mHz (this is the thick, expensive cable that has the lowest losses.)

Tengo los siguientes:
- De 100%: 2m, 6m, 6m
- De 90%: 11m
- De 80%: 12m, 10m, 8m, 6m


### PEDIDOS A CHINA

Paypal para transferir

sd77 to <EMAIL>
usd730 to <EMAIL>


Voy a necesitar que me pases estos datos para que te coticen en China:

Name:
Email:
Phone:
TAX ID:
Address:
City:
Postal Code:
State:
Country:
Pay by: Paypal and Credit Card

Hi Lisa, I have a customer from Chile that wants to buy the following:

Items:
1 Reader/Writer USB Desktop
1 Reader Invelion YR8900 (8 ports)
1 Reader Invelion YR8700 (4 ports)
4 Antennas Invelion YR9028 (9 dbi)
4 Antenna Cables (6m, 5m, 4m, 3m)
500 RFID Disposable Tags (Encoded with the same EPC)
500 RFID Reusable Ankle Tags with Straps


### DIRECCIONES DE CLIENTES

Name: Andres Misiak
Email: <EMAIL>
Phone: +54 ************
TAX ID (CUIT): 20-18787460-3
Address: Conrado Villegas 820
City: Neuquén Capital
Postal Code: 8300
State: Neuquén
Country: Argentina
Pay by: Paypal and Credit Card

Name: Julieta Romina Bouzo
Email: <EMAIL>
Phone: +54 ************
TAX ID (CUIT): 27-31240532-1
Address: Conrado Villegas 820
City: Neuquén Capital
Postal Code: 8300
State: Neuquén
Country: Argentina

Name: Óscar Daniel Fuentes Ramirez
Email: <EMAIL>
Phone: +54 ************
TAX ID (RUT): 18.197.365-k
Address: El Valle 141
City: Cajón de Vilcun
Postal Code: 4010000
State: Temuco
Country: Chile
Pay by: Paypal

Name: Miguel Andres Muñoz Pantoja
TAX ID (RUT): 12748315
Phone: +57 3215460235
Email: <EMAIL>
Address: Calle 12 N. 39-42 Mariluz 1
City: Pasto
Postal Code: 520001
State: Departamento Nariño
Country: Colombia
